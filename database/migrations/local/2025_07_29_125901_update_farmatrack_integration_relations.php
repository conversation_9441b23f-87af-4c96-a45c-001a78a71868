<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class UpdateFarmatrackIntegrationRelations extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        // Set PostgreSQL configuration for optimal performance
        DB::statement("SET maintenance_work_mem = '512MB'");
        DB::statement('SET synchronous_commit = OFF');

        // Start transaction with serializable isolation
        DB::transaction(function () {
            $this->createMigrationLogTable();
            $this->logStep('migration_started');

            try {
                // Step 1: Create comprehensive backups
                $this->createBackupTables();

                // Step 2: Select integrations to keep (one per organization)
                $this->selectIntegrationsToKeep();

                // Step 3: Select machines to keep (one per wialon_unit_imei per organization)
                $this->selectMachinesToKeep();

                // Step 4: Migrate foreign key references
                $this->migrateMachineEvents();
                $this->migrateMachineTasks();
                $this->migrateImplements();
                $this->migrateIntegrationReports();
                $this->updateMachineIntegrationReferences();

                // Step 5: Modify schema
                $this->modifyIntegrationSchema();

                // Step 6: Delete duplicates
                $this->deleteDuplicateMachines();
                $this->deleteDuplicateIntegrations();

                // Step 7: Final validation
                $this->validateMigration();

                // Step 8: Update statistics
                $this->updateTableStatistics();

                $this->logStep('migration_completed', 'completed');
            } catch (\Exception $e) {
                $this->logStep('migration_failed', 'failed', $e->getMessage());

                throw $e;
            }
        }, 5); // 5 attempts for deadlock retry

        // Reset PostgreSQL configuration
        DB::statement('RESET maintenance_work_mem');
        DB::statement('SET synchronous_commit = ON');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::transaction(function () {
            $this->logStep('rollback_initiated');

            try {
                // Disable foreign key checks for faster restoration
                DB::statement('SET session_replication_role = replica');

                // Restore tables in dependency order
                DB::statement('TRUNCATE su_machine_events, su_machine_tasks, su_machines_implements, 
                             su_integrations_reports, su_machine_units, su_integration CASCADE');

                // Restore data from backups
                DB::statement('INSERT INTO su_integration SELECT * FROM su_integration_backup');
                DB::statement('INSERT INTO su_machine_units SELECT * FROM su_machine_units_backup');
                DB::statement('INSERT INTO su_machine_events SELECT * FROM su_machine_events_backup');
                DB::statement('INSERT INTO su_machine_tasks SELECT * FROM su_machine_tasks_backup');
                DB::statement('INSERT INTO su_machines_implements SELECT * FROM su_machines_implements_backup');
                DB::statement('INSERT INTO su_integrations_reports SELECT * FROM su_integrations_reports_backup');

                // Re-enable foreign key checks
                DB::statement('SET session_replication_role = DEFAULT');

                // Drop backup tables
                $this->dropBackupTables();

                // Drop migration log table
                Schema::dropIfExists('migration_log');

                $this->logStep('rollback_completed', 'completed');
            } catch (\Exception $e) {
                DB::statement('SET session_replication_role = DEFAULT');
                $this->logStep('rollback_failed', 'failed', $e->getMessage());

                throw $e;
            }
        }, 5);
    }

    /**
     * Create migration log table.
     */
    private function createMigrationLogTable(): void
    {
        if (!Schema::hasTable('migration_log')) {
            Schema::create('migration_log', function (Blueprint $table) {
                $table->id();
                $table->string('step_name', 100);
                $table->timestamp('started_at')->default(DB::raw('NOW()'));
                $table->timestamp('completed_at')->nullable();
                $table->integer('row_count')->default(0);
                $table->integer('affected_rows')->default(0);
                $table->string('status', 20)->default('started');
                $table->text('error_message')->nullable();
                $table->integer('duration_seconds')->nullable();
            });

            DB::statement("ALTER TABLE migration_log ADD CONSTRAINT valid_status 
                          CHECK (status IN ('started', 'completed', 'failed', 'rolled_back'))");
        }
    }

    /**
     * Create backup tables.
     */
    private function createBackupTables(): void
    {
        $this->logStep('backup_creation');

        $tables = [
            'su_integration',
            'su_machine_units',
            'su_machine_events',
            'su_machine_tasks',
            'su_machines_implements',
            'su_integrations_reports',
        ];

        $totalRows = 0;

        foreach ($tables as $table) {
            DB::statement("DROP TABLE IF EXISTS {$table}_backup CASCADE");
            DB::statement("CREATE TABLE {$table}_backup AS SELECT * FROM {$table}");

            $count = DB::table("{$table}_backup")->count();
            $totalRows += $count;

            // Verify backup integrity
            $originalCount = DB::table($table)->count();
            if ($count !== $originalCount) {
                throw new \Exception("Backup verification failed for table {$table}: original={$originalCount}, backup={$count}");
            }
        }

        $this->logStep('backup_creation', 'completed', null, $totalRows);
    }

    /**
     * Select integrations to keep (one per organization).
     */
    private function selectIntegrationsToKeep(): void
    {
        $this->logStep('select_integrations');

        DB::statement('DROP TABLE IF EXISTS selected_integrations');

        DB::statement("
            WITH ranked_integrations AS (
                SELECT *,
                    ROW_NUMBER() OVER (
                        PARTITION BY organization_id 
                        ORDER BY 
                            CASE WHEN status = 'Active' THEN 1 ELSE 2 END,
                            updated_at DESC NULLS LAST,
                            created_at DESC
                    ) as rn
                FROM su_integration
            )
            SELECT * INTO TEMP selected_integrations FROM ranked_integrations WHERE rn = 1
        ");

        $count = DB::table('selected_integrations')->count();
        $this->logStep('select_integrations', 'completed', null, $count);
    }

    /**
     * Select machines to keep (one per wialon_unit_imei per organization).
     */
    private function selectMachinesToKeep(): void
    {
        $this->logStep('select_machines');

        DB::statement('DROP TABLE IF EXISTS selected_machines');

        DB::statement('
            WITH machine_rankings AS (
                SELECT 
                    smu.*,
                    COALESCE(COUNT(DISTINCT sme.id), 0) as event_count,
                    COALESCE(COUNT(DISTINCT smt.id), 0) as task_count,
                    ROW_NUMBER() OVER (
                        PARTITION BY smu.wialon_unit_imei, si.organization_id 
                        ORDER BY 
                            smu.last_communication DESC NULLS LAST,
                            COALESCE(COUNT(DISTINCT sme.id), 0) + COALESCE(COUNT(DISTINCT smt.id), 0) DESC,
                            smu.id DESC
                    ) as rn
                FROM su_machine_units smu
                JOIN su_integration si ON smu.integration_id = si.id
                LEFT JOIN su_machine_events sme ON smu.id = sme.machine_id
                LEFT JOIN su_machine_tasks smt ON smu.id = smt.machine_unit_id
                GROUP BY smu.id, si.organization_id
            )
            SELECT * INTO TEMP selected_machines FROM machine_rankings WHERE rn = 1
        ');

        $count = DB::table('selected_machines')->count();
        $this->logStep('select_machines', 'completed', null, $count);
    }

    /**
     * Migrate machine events to consolidated machines.
     */
    private function migrateMachineEvents(): void
    {
        $this->logStep('migrate_machine_events');

        $deletedCount = DB::delete('
            WITH events_to_delete AS (
                SELECT DISTINCT sme.id
                FROM su_machine_events sme
                JOIN su_machine_units smu_old ON sme.machine_id = smu_old.id
                JOIN su_integration si_old ON smu_old.integration_id = si_old.id
                JOIN selected_machines sm ON (
                    sm.wialon_unit_imei = smu_old.wialon_unit_imei
                    AND sm.organization_id = si_old.organization_id
                    AND sm.id != smu_old.id
                )
                WHERE EXISTS (
                    SELECT 1 FROM su_machine_events existing
                    WHERE existing.machine_id = sm.id
                    AND COALESCE(existing.plot_id, -1) = COALESCE(sme.plot_id, -1)
                    AND COALESCE(existing.machine_implement_id, -1) = COALESCE(sme.machine_implement_id, -1)
                    AND existing.date = sme.date
                    AND existing.start_date = sme.start_date
                    AND existing.end_date = sme.end_date
                    AND existing.type = sme.type
                )
            )
            DELETE FROM su_machine_events 
            WHERE id IN (SELECT id FROM events_to_delete)
        ');

        $affected = DB::statement('
            WITH update_mapping AS (
                SELECT 
                    sme.id as event_id,
                    sm.id as new_machine_id
                FROM su_machine_events sme
                JOIN su_machine_units smu_old ON sme.machine_id = smu_old.id
                JOIN su_integration si_old ON smu_old.integration_id = si_old.id
                JOIN selected_machines sm ON (
                    sm.wialon_unit_imei = smu_old.wialon_unit_imei
                    AND sm.organization_id = si_old.organization_id
                    AND sm.id != smu_old.id
                )
            )
            UPDATE su_machine_events SET machine_id = um.new_machine_id
            FROM update_mapping um WHERE su_machine_events.id = um.event_id
        ');

        $this->logStep('migrate_machine_events', 'completed', null, 0, $affected);
    }

    /**
     * Migrate machine tasks to consolidated machines.
     */
    private function migrateMachineTasks(): void
    {
        $this->logStep('migrate_machine_tasks');

        // First, update tasks that can be migrated to selected machines
        $affected = DB::statement('
            WITH update_mapping AS (
                SELECT smt.id as task_id, sm.id as new_machine_id
                FROM su_machine_tasks smt
                JOIN su_machine_units smu_old ON smt.machine_unit_id = smu_old.id
                JOIN su_integration si_old ON smu_old.integration_id = si_old.id
                JOIN selected_machines sm ON (
                    sm.wialon_unit_imei = smu_old.wialon_unit_imei
                    AND sm.organization_id = si_old.organization_id
                    AND sm.id != smu_old.id
                )
            )
            UPDATE su_machine_tasks SET machine_unit_id = um.new_machine_id
            FROM update_mapping um WHERE su_machine_tasks.id = um.task_id
        ');

        // Delete tasks that cannot be migrated (no corresponding selected machine)
        // But preserve tasks that have no machine_unit_id (NULL values)
        $deletedTasks = DB::delete('
            DELETE FROM su_machine_tasks smt
            WHERE smt.machine_unit_id IS NOT NULL
            AND smt.machine_unit_id NOT IN (SELECT id FROM selected_machines)
            AND NOT EXISTS (
                SELECT 1 FROM su_machine_units smu_old
                JOIN su_integration si_old ON smu_old.integration_id = si_old.id
                JOIN selected_machines sm ON (
                    sm.wialon_unit_imei = smu_old.wialon_unit_imei
                    AND sm.organization_id = si_old.organization_id
                )
                WHERE smu_old.id = smt.machine_unit_id
            )
        ');

        $this->logStep('migrate_machine_tasks', 'completed', null, 0, $affected + $deletedTasks);
    }

    /**
     * Migrate implements to consolidated integrations.
     */
    private function migrateImplements(): void
    {
        $this->logStep('migrate_implements');

        $affected = DB::statement('
            WITH update_mapping AS (
                SELECT smi.id as implement_id, si.id as new_integration_id
                FROM su_machines_implements smi
                JOIN su_integration si_old ON smi.integration_id = si_old.id
                JOIN selected_integrations si ON (
                    si.organization_id = si_old.organization_id
                    AND si.id != si_old.id
                )
            )
            UPDATE su_machines_implements SET integration_id = um.new_integration_id
            FROM update_mapping um WHERE su_machines_implements.id = um.implement_id
        ');

        $this->logStep('migrate_implements', 'completed', null, 0, $affected);
    }

    /**
     * Migrate integration reports to consolidated integrations.
     */
    private function migrateIntegrationReports(): void
    {
        $this->logStep('migrate_reports');

        $affected = DB::statement('
            WITH update_mapping AS (
                SELECT sir.id as report_id, si.id as new_integration_id
                FROM su_integrations_reports sir
                JOIN su_integration si_old ON sir.integration_id = si_old.id
                JOIN selected_integrations si ON (
                    si.organization_id = si_old.organization_id
                    AND si.id != si_old.id
                )
            )
            UPDATE su_integrations_reports SET integration_id = um.new_integration_id
            FROM update_mapping um WHERE su_integrations_reports.id = um.report_id
        ');

        $this->logStep('migrate_reports', 'completed', null, 0, $affected);
    }

    /**
     * Update machine unit integration references.
     */
    private function updateMachineIntegrationReferences(): void
    {
        $this->logStep('update_machine_integrations');

        $affected = DB::statement('
            UPDATE su_machine_units SET integration_id = si.id
            FROM selected_integrations si
            JOIN su_integration si_old ON (
                si.organization_id = si_old.organization_id
                AND si.id != si_old.id
            )
            WHERE su_machine_units.integration_id = si_old.id
        ');

        $this->logStep('update_machine_integrations', 'completed', null, 0, $affected);
    }

    /**
     * Modify integration schema.
     */
    private function modifyIntegrationSchema(): void
    {
        $this->logStep('modify_columns');

        Schema::table('su_integration', function (Blueprint $table) {
            // Drop old columns
            if (Schema::hasColumn('su_integration', 'contract_id')) {
                $table->dropColumn('contract_id');
            }
            if (Schema::hasColumn('su_integration', 'package_period')) {
                $table->dropColumn('package_period');
            }
        });

        $this->logStep('modify_columns', 'completed');
    }

    /**
     * Delete duplicate machines.
     */
    private function deleteDuplicateMachines(): void
    {
        $this->logStep('delete_duplicate_machines');

        // Delete events from machines that will be deleted
        $deletedEvents = DB::delete('
            DELETE FROM su_machine_events
            WHERE machine_id NOT IN (SELECT id FROM selected_machines)
        ');

        // Note: Tasks are already handled in migrateMachineTasks() method
        // to ensure proper migration before deletion

        $affected = DB::delete('
            DELETE FROM su_machine_units
            WHERE id NOT IN (SELECT id FROM selected_machines)
        ');

        $this->logStep('delete_duplicate_machines', 'completed', null, 0, $affected + $deletedEvents);
    }

    /**
     * Delete duplicate integrations.
     */
    private function deleteDuplicateIntegrations(): void
    {
        $this->logStep('delete_duplicate_integrations');

        $affected = DB::delete('
            DELETE FROM su_integration 
            WHERE id NOT IN (SELECT id FROM selected_integrations)
        ');

        $this->logStep('delete_duplicate_integrations', 'completed', null, 0, $affected);
    }

    /**
     * Validate migration results.
     */
    private function validateMigration(): void
    {
        $this->logStep('final_validation');

        $errors = [];

        // Check for multiple integrations per organization
        $multipleIntegrations = DB::select('
            SELECT organization_id, COUNT(*) as count
            FROM su_integration 
            GROUP BY organization_id 
            HAVING COUNT(*) > 1
        ');

        if (count($multipleIntegrations) > 0) {
            $errors[] = sprintf('Found %d organizations with multiple integrations', count($multipleIntegrations));
        }

        // Check for orphaned events
        $orphanedEvents = DB::selectOne('
            SELECT COUNT(*) as count
            FROM su_machine_events sme
            LEFT JOIN su_machine_units smu ON sme.machine_id = smu.id
            WHERE smu.id IS NULL
        ');

        if ($orphanedEvents->count > 0) {
            $errors[] = sprintf('Found %d orphaned machine events', $orphanedEvents->count);
        }

        // Check for orphaned tasks
        $orphanedTasks = DB::selectOne('
            SELECT COUNT(*) as count
            FROM su_machine_tasks smt
            LEFT JOIN su_machine_units smu ON smt.machine_unit_id = smu.id
            WHERE smu.id IS NULL
            and smt.machine_unit_id is not null
        ');

        if ($orphanedTasks->count > 0) {
            $errors[] = sprintf('Found %d orphaned machine tasks', $orphanedTasks->count);
        }

        // Check for duplicate machines per organization
        $duplicateMachines = DB::select('
            SELECT smu.wialon_unit_imei, si.organization_id, COUNT(*) as count
            FROM su_machine_units smu
            JOIN su_integration si ON smu.integration_id = si.id
            GROUP BY smu.wialon_unit_imei, si.organization_id
            HAVING COUNT(*) > 1
        ');

        if (count($duplicateMachines) > 0) {
            $errors[] = sprintf('Found %d duplicate machines per organization', count($duplicateMachines));
        }

        if (!empty($errors)) {
            $errorMessage = 'Migration validation failed: ' . implode('. ', $errors);
            $this->logStep('final_validation', 'failed', $errorMessage);

            throw new \Exception($errorMessage);
        }

        $this->logStep('final_validation', 'completed');
    }

    /**
     * Update table statistics for optimal query performance.
     */
    private function updateTableStatistics(): void
    {
        DB::statement('ANALYZE su_integration, su_machine_units, su_machine_events, 
                      su_machine_tasks, su_machines_implements, su_integrations_reports');
    }

    /**
     * Drop backup tables.
     */
    private function dropBackupTables(): void
    {
        $tables = [
            'su_integration_backup',
            'su_machine_units_backup',
            'su_machine_events_backup',
            'su_machine_tasks_backup',
            'su_machines_implements_backup',
            'su_integrations_reports_backup',
        ];

        foreach ($tables as $table) {
            Schema::dropIfExists($table);
        }

        // Drop temporary tables
        DB::statement('DROP TABLE IF EXISTS selected_integrations');
        DB::statement('DROP TABLE IF EXISTS selected_machines');
    }

    /**
     * Log migration step.
     *
     * @param ?string $errorMessage
     */
    private function logStep(
        string $stepName,
        string $status = 'started',
        ?string $errorMessage = null,
        int $rowCount = 0,
        int $affectedRows = 0
    ): void {
        if ('started' === $status) {
            DB::table('migration_log')->insert([
                'step_name' => $stepName,
                'status' => $status,
                'row_count' => $rowCount,
                'affected_rows' => $affectedRows,
                'error_message' => $errorMessage,
            ]);
        } else {
            $step = DB::table('migration_log')
                ->where('step_name', $stepName)
                ->where('status', 'started')
                ->orderBy('started_at', 'desc')
                ->first();

            if ($step) {
                $duration = DB::selectOne('
                    SELECT EXTRACT(EPOCH FROM (NOW() - ?::timestamp))::INTEGER as duration
                ', [$step->started_at]);

                DB::table('migration_log')
                    ->where('id', $step->id)
                    ->update([
                        'completed_at' => now(),
                        'status' => $status,
                        'row_count' => $rowCount ?: $step->row_count,
                        'affected_rows' => $affectedRows ?: $step->affected_rows,
                        'error_message' => $errorMessage,
                        'duration_seconds' => $duration->duration,
                    ]);
            }
        }
    }
}
