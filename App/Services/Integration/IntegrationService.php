<?php

namespace App\Services\Integration;

use App\Classes\CMS\ContractService;
use App\Classes\CMS\PackageService;
use App\Exceptions\NotFoundException;
use App\Helpers\Helper;
use App\Models\Integration;
use App\Models\IntegrationAddress;
use App\Models\IntegrationReportsTypes;
use App\Models\MachineImplement;
use App\Models\MachineImplementWorkOperation;
use App\Models\MachineUnit;
use App\Services\Wialon\WialonService;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class IntegrationService
{
    private $contractService;
    private $wialonService;

    private $packageService;

    public function __construct(ContractService $contractService, WialonService $wialonService, PackageService $packageService)
    {
        $this->contractService = $contractService;
        $this->wialonService = $wialonService;
        $this->packageService = $packageService;
    }

    public function getIntegrations(int $organizationId, string $packageSlugShort)
    {
        $integrations = Integration::where('organization_id', $organizationId)->get();

        $queryParams = [
            'filter' => [
                'slug_short' => $packageSlugShort,
                'service_provider_id' => Auth::user()->lastChosenOrganization->service_provider_id,
            ],
        ];

        $packages = $this->packageService->list($queryParams);

        $emptyIntegration = array_fill_keys(Integration::getTableColumns(), null);
        $emptyIntegration['organization_id'] = $organizationId;
        $emptyIntegration['status'] = null;
        $emptyIntegration['package_slug_short'] = $packageSlugShort;

        $result = [];
        foreach ($packages as $package) {
            $emptyIntegration['package_id'] = $package['id'];

            $integrationIndex = array_search($package['id'], array_column($integrations->toArray(), 'package_id'));
            $integration = false !== $integrationIndex ? $integrations[$integrationIndex]->toArray() : $emptyIntegration;
            $integration['package_style'] = $package['style'];
            $result[] = $integration;
        }

        return $result;
    }

    /**
     * Create new integration of type FT. Store machine units and implements.
     * Create unit group and report templates in Wialon.
     * Create IntegrationReportTypes and IntegrationReports for the new integration.
     *
     * @return Integration The created integration
     */
    public function createFTIntegration(array $integrationData, array $machineUnitsData, array $machineImplementsData): Integration
    {
        $wialonReportTemplatesId = [];
        $wialonUnitGroupId = null;
        $wialonResourceId = null;

        DB::beginTransaction();

        try {
            // Store integration data to DB
            $integration = $this->storeIntegration($integrationData);
            $machineUnits = $this->storeMachineUnits($integration, $machineUnitsData);
            $machineImplements = $this->storeMachineImplements($integration, $machineImplementsData);

            // Get Wialon user resource id
            $flags = 1; // base flag
            $wialonResources = $this->wialonService->searchItems(WialonService::ITEMS_TYPE_RESOURCE, '*', '*', $flags);
            $wialonResourceId = $wialonResources['items'][0]['id'] ?? null; // first resource

            if (!$wialonResourceId) {
                throw new NotFoundException('Wialon resources not found!');
            }

            // Create machines unit group in Wialon
            $machinesWialonUnitId = array_column($machineUnits, 'wialon_unit_id');
            $wialonUnitGroupName = "Agrimi Integration {$integration->id} - Units";
            $wialonUnitGroupId = $this->wialonService->createUnitGroup($integration->remote_user_id, $wialonUnitGroupName, $machinesWialonUnitId);

            // Create machines report templates in Wialon
            $reportTemplatesConfig = Config::get('wialon.report-templates');
            $reportTemplatesParams = array_map(function ($templateParams) use ($wialonResourceId, $integration) {
                $reportName = $templateParams['n'] ?? '';
                $templateParams['n'] = "Agrimi Integration {$integration->id} - {$reportName}";
                $templateParams['itemId'] = $wialonResourceId;

                return $templateParams;
            }, $reportTemplatesConfig);
            $wialonReportTemplatesId = $this->wialonService->createReportTemplates($reportTemplatesParams);

            // Create reports in Wialon
            $this->createIntegrationReports($integration, $wialonResourceId, $wialonUnitGroupId, $wialonReportTemplatesId);
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();

            if ($wialonResourceId && count($wialonReportTemplatesId)) {
                $wialonReportTemplatesId = array_values($wialonReportTemplatesId);
                $this->wialonService->deleteWialonReportTemplates($wialonResourceId, $wialonReportTemplatesId);
            }

            if ($wialonUnitGroupId) {
                $this->wialonService->deleteItem($wialonUnitGroupId);
            }

            throw $e;
        }

        return $integration;
    }

    /**
     * Update FT integration.
     *
     * @return Integration The updated integration
     */
    public function updateFTIntegration(Integration $integration, array $integrationNewData, array $machineUnitsData, array $machineImplementsData): Integration
    {
        DB::beginTransaction();

        try {
            $updatedIntegration = $this->updateIntegration($integration, $integrationNewData);
            $this->updateMachineUnits($integration, $machineUnitsData);
            $this->updateMachineImplements($integration, $machineImplementsData);

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();

            throw $e;
        }

        return $updatedIntegration;
    }

    /**
     * Update integration.
     *
     * @return Integration The updated integration
     */
    public function updateIntegration(Integration $integration, array $integrationNewData): Integration
    {
        $integrationAddress = IntegrationAddress::findOrFail($integrationNewData['integration_address']);
        $wialonData = $this->wialonService->login($integrationNewData['token'], $integrationAddress->getBaseUrl());

        if ($integration->remote_user_id !== $wialonData['user']['id']) {
            throw new Exception('Cannot change integration user!');
        }

        $integration->update($integrationNewData);

        return $integration->load('integrationAddress');
    }

    /**
     * Store integration to DB.
     *
     * @param array $integrationData = [
     *                               'token' => string,
     *                               'organization_id' => integer,
     *                               'contract_id' => integer,
     *                               'package_id' => integer,
     *                               'package_slug_short' => string,
     *                               'package_period' => string,
     *                               'integration_address' => array,
     *                               'status' => string,
     *                               ]
     *
     * @return Integration The stored integration
     */
    public function storeIntegration(array $integrationData): Integration
    {
        $integration = new Integration();
        $integration->fill($integrationData);
        $integrationAddressBaseUrl = $integration->integrationAddress()->first()->getBaseUrl();

        $wialonData = $this->wialonService->login($integration->token, $integrationAddressBaseUrl);
        $integration->remote_username = $wialonData['user']['nm'];
        $integration->remote_user_id = $wialonData['user']['id'];
        $integration->save();

        return $integration->load('integrationAddress');
    }

    /**
     * Store machine units to DB.
     *
     * @param array $machineUnits = [
     *                            [
     *                            "id" => integer,
     *                            "organization_id" => integer,
     *                            "name" => string,
     *                            "wialon_unit_imei" => bigint,
     *                            "type" => string,
     *                            "last_communication" => string,
     *                            "last_position_geojson" => [
     *                            "type" => string,
     *                            "coordinates" => [
     *                            float,
     *                            float,
     *                            ]
     *                            },
     *                            "wialon_unit_id" => integer,
     *                            "integration_id" => integer,
     *                            "existing" => boolean
     *                            ],
     *                            ...
     *                            ]
     *
     * @return array The stored units
     */
    private function storeMachineUnits(Integration $integration, array $machineUnitsData): array
    {
        $epsgProj = Config::get('globals.DEFAULT_DB_CRS');
        $machineUnits = array_map(function ($machineUnit) use ($integration, $epsgProj) {
            $lastPosition = null;

            if ($machineUnit['last_position_geojson']) {
                $lastPositionGeoJson = json_encode($machineUnit['last_position_geojson']);
                $lastPosition = DB::raw("ST_Transform(ST_SetSRID(ST_GeomFromGeoJSON('{$lastPositionGeoJson}'), 4326), {$epsgProj})");
            }

            return [
                'organization_id' => $integration->organization_id,
                'integration_id' => $integration->id,
                'wialon_unit_imei' => $machineUnit['wialon_unit_imei'],
                'wialon_unit_id' => $machineUnit['wialon_unit_id'],
                'name' => $machineUnit['name'],
                'type' => $machineUnit['type'],
                'last_communication' => $machineUnit['last_communication'],
                'last_position' => $lastPosition,
            ];
        }, $machineUnitsData);

        MachineUnit::insert($machineUnits);

        return $machineUnits;
    }

    /**
     * Update machine units.
     *
     * @param array $machineUnits = [
     *                            [
     *                            "id" => integer,
     *                            "organization_id" => integer,
     *                            "name" => string,
     *                            "wialon_unit_imei" => bigint,
     *                            "type" => string,
     *                            "last_communication" => string,
     *                            "last_position_geojson" => [
     *                            "type" => string,
     *                            "coordinates" => [
     *                            float,
     *                            float,
     *                            ]
     *                            },
     *                            "wialon_unit_id" => integer,
     *                            "integration_id" => integer,
     *                            "existing" => boolean
     *                            ],
     *                            ...
     *                            ]
     */
    private function updateMachineUnits(Integration $integration, array $machineUnitsData)
    {
        $machineUnitsToCreate = [];
        $machineUnitsToUpdate = [];
        $machineUnitsToDelete = [];

        foreach ($machineUnitsData as $machineUnit) {
            if ('create' === $machineUnit['mode']) {
                $machineUnitsToCreate[] = $machineUnit;

                continue;
            }

            if (
                'update' === $machineUnit['mode'] && isset($machineUnit['id'])) {
                $machineUnitsToUpdate[] = $machineUnit;

                continue;
            }

            if ('delete' === $machineUnit['mode'] && isset($machineUnit['id'])) {
                $machineUnitsToDelete[] = $machineUnit['id'];

                continue;
            }
        }

        $this->storeMachineUnits($integration, $machineUnitsToCreate);

        Helper::multiUpdate(
            MachineUnit::getTableName(),
            $machineUnitsToUpdate,
            'id',
            ['type'],
            ['type' => 'machine_unit_types_enum']
        );

        MachineUnit::whereIn('id', $machineUnitsToDelete)->delete();

        $wialonUnitIds = MachineUnit::where('integration_id', $integration->id)->distinct()->pluck('wialon_unit_id')->toArray();
        $machineLastDataReportTypeParams = $integration->reportsTypes()
            ->where('name', IntegrationReportsTypes::MACHINES_LAST_DATA)
            ->pluck('params')
            ->first();

        $machineLastDataReportTypeParams = json_decode($machineLastDataReportTypeParams, true);

        $wialonUnitGroupId = $machineLastDataReportTypeParams['exec_params']['reportObjectId'] ?? null;
        $this->wialonService->setUnitsToGroup($wialonUnitGroupId, $wialonUnitIds);
    }

    /**
     * Store machine implements to DB.
     *
     * @param array $machineImplements = [
     *                                 [
     *                                 "id" => integer,
     *                                 "organization_id" => integer,
     *                                 "name" => string,
     *                                 "width" => float,
     *                                 "status" => string,
     *                                 "integration_id" => integer,
     *                                 "wialon_unit_id" => integer,
     *                                 "existing" => boolean,
     *                                 "work_operations" => string[]
     *                                 ],
     *                                 ...
     *                                 ]
     *
     * @return array The stored implements
     */
    private function storeMachineImplements(Integration $integration, array $machineImplementsData): array
    {
        $machineImplements = [];
        $workOperations = [];
        foreach ($machineImplementsData as $data) {
            $machineImplement = MachineImplement::create([
                'organization_id' => $integration->organization_id,
                'integration_id' => $integration->id,
                'wialon_unit_id' => $data['wialon_unit_id'],
                'name' => $data['name'],
                'width' => $data['width'],
                'status' => $data['status'],
            ]);

            if (isset($data['work_operation_ids'])) {
                foreach ($data['work_operation_ids'] as $workOperationId) {
                    $workOperations[] = [
                        'implement_id' => $machineImplement->id,
                        'work_operation_id' => $workOperationId,
                    ];
                }
            }

            $machineImplements[] = $machineImplement;
        }

        if (count($workOperations) > 0) {
            MachineImplementWorkOperation::insert($workOperations);
        }

        return $machineImplements;
    }

    /**
     * Update machine implements.
     *
     * @param array $machineImplementsData = [
     *                                     [
     *                                     "id" => integer,
     *                                     "organization_id" => integer,
     *                                     "name" => string,
     *                                     "width" => float,
     *                                     "status" => string,
     *                                     "integration_id" => integer,
     *                                     "wialon_unit_id" => integer,
     *                                     "existing" => boolean
     *                                     ],
     *                                     ...
     *                                     ]
     */
    private function updateMachineImplements(Integration $integration, array $machineImplementsData)
    {
        $machineImplementsToCreate = [];
        $machineImplementsToUpdate = [];
        $machineImplementsToDelete = [];
        $machineImplementsWorkOperationsToDelete = [];
        $machineImplementsWorkOperationsToCreate = [];

        foreach ($machineImplementsData as $machineImplement) {
            if ('create' === $machineImplement['mode']) {
                $machineImplementsToCreate[] = $machineImplement;

                continue;
            }

            if (
                'update' === $machineImplement['mode'] && isset($machineImplement['id'])) {
                $machineImplementsToUpdate[] = $machineImplement;

                if (!isset($machineImplement['work_operation_ids'])) {
                    continue;
                }

                $machineImplementsWorkOperationsToDelete[] = $machineImplement['id'];
                foreach ($machineImplement['work_operation_ids'] as $workOperationId) {
                    $machineImplementsWorkOperationsToCreate[] = [
                        'implement_id' => $machineImplement['id'],
                        'work_operation_id' => $workOperationId,
                    ];
                }

                continue;
            }

            if ('delete' === $machineImplement['mode'] && isset($machineImplement['id'])) {
                $machineImplementsToDelete[] = $machineImplement['id'];

                continue;
            }
        }

        $this->storeMachineImplements($integration, $machineImplementsToCreate);

        Helper::multiUpdate(
            MachineImplement::getTableName(),
            $machineImplementsToUpdate,
            'id',
            ['width', 'status'],
            ['status' => 'machine_implement_status_enum']
        );

        MachineImplementWorkOperation::whereIn('implement_id', $machineImplementsWorkOperationsToDelete)->delete();
        MachineImplementWorkOperation::insert($machineImplementsWorkOperationsToCreate);

        MachineImplement::whereIn('id', $machineImplementsToDelete)->delete();
    }

    private function createIntegrationReports(Integration $integration, int $wialonResourceId, int $wialonUnitGroupId, array $wialonReportTemplatesId)
    {
        // TODO: Create separate table for report templates
        $reportTypeTemplateMap = [
            IntegrationReportsTypes::MACHINE_EVENTS => 'machines_current_fuel',
            IntegrationReportsTypes::MACHINE_TRACK => 'machines_current_fuel',
            IntegrationReportsTypes::MACHINES_CURRENT => 'machines_current',
            IntegrationReportsTypes::MACHINES_LAST_DATA => 'machines_current',
            IntegrationReportsTypes::IMPLEMENTS_SYNC => null,
        ];

        $integrationAddressBaseUrl = $integration->integrationAddress()->first()->getBaseUrl();

        $machineEventsReport = new IntegrationReportsTypes();
        $machineEventsReport->name = IntegrationReportsTypes::MACHINE_EVENTS;
        $machineEventsReport->execution = IntegrationReportsTypes::SCHEDULED;
        $machineEventsReport->period = '0 3 * * *';
        $machineEventsReport->url = $integrationAddressBaseUrl;
        $reportTemplate = $reportTypeTemplateMap[IntegrationReportsTypes::MACHINE_EVENTS];
        $machineEventsReport->params = json_encode([
            'exec_params' => [
                'reportResourceId' => $wialonResourceId,
                'reportTemplateId' => $wialonReportTemplatesId[$reportTemplate] ?? null,
                'reportTemplate' => null,
                'reportObjectId' => $wialonUnitGroupId,
                'reportObjectSecId' => 0,
                'interval' => [
                    'flags' => 16777218,
                    'from' => 0,
                    'to' => 1,
                ],
                'reportObjectIdList' => [],
            ],
            'exec_result_params' => [
                'tableIndex' => 0,
                'config' => [
                    'type' => 'range',
                    'data' => [
                        'from' => 0,
                        'to' => 0,
                        'level' => 1,
                        'flat' => 1,
                    ],
                ],
            ],
        ]);
        $machineEventsReport->save();
        $integration->reports()->create([
            'integration_reports_types_id' => $machineEventsReport->id,
        ]);

        $machineCurrentReport = new IntegrationReportsTypes();
        $machineCurrentReport->name = IntegrationReportsTypes::MACHINES_CURRENT;
        $machineCurrentReport->execution = IntegrationReportsTypes::SCHEDULED;
        $machineCurrentReport->period = '*/1 * * * *';
        $machineCurrentReport->url = $integrationAddressBaseUrl;
        $reportTemplate = $reportTypeTemplateMap[IntegrationReportsTypes::MACHINES_CURRENT];
        $machineCurrentReport->params = json_encode([
            'exec_params' => [
                'reportResourceId' => $wialonResourceId,
                'reportTemplateId' => $wialonReportTemplatesId[$reportTemplate] ?? null,
                'reportTemplate' => null,
                'reportObjectId' => $wialonUnitGroupId,
                'reportObjectSecId' => 0,
                'interval' => [
                    'flags' => 16777344,
                    'from' => 0,
                    'to' => 5,
                ],
                'reportObjectIdList' => [],
            ],
            'exec_result_params' => [
                'tableIndex' => 0,
                'config' => [
                    'type' => 'range',
                    'data' => [
                        'from' => 0,
                        'to' => 0,
                        'level' => 1,
                        'flat' => 1,
                    ],
                ],
            ],
        ]);
        $machineCurrentReport->save();
        $integration->reports()->create([
            'integration_reports_types_id' => $machineCurrentReport->id,
        ]);

        $machineLastDataReport = new IntegrationReportsTypes();
        $machineLastDataReport->name = IntegrationReportsTypes::MACHINES_LAST_DATA;
        $machineLastDataReport->execution = IntegrationReportsTypes::ON_REQUEST;
        $machineLastDataReport->period = null;
        $machineLastDataReport->url = $integrationAddressBaseUrl;
        $reportTemplate = $reportTypeTemplateMap[IntegrationReportsTypes::MACHINES_LAST_DATA];
        $machineLastDataReport->params = json_encode([
            'exec_params' => [
                'reportResourceId' => $wialonResourceId,
                'reportTemplateId' => $wialonReportTemplatesId[$reportTemplate] ?? null,
                'reportTemplate' => null,
                'reportObjectId' => $wialonUnitGroupId,
                'reportObjectSecId' => 0,
                'interval' => [
                    'flags' => 16777344,
                    'from' => 0,
                    'to' => 5,
                ],
                'reportObjectIdList' => [],
            ],
            'exec_result_params' => [
                'tableIndex' => 1,
                'config' => [
                    'type' => 'range',
                    'data' => [
                        'from' => 0,
                        'to' => 0,
                        'level' => 0,
                        'flat' => 1,
                    ],
                ],
            ],
        ]);
        $machineLastDataReport->save();
        $integration->reports()->create([
            'integration_reports_types_id' => $machineLastDataReport->id,
        ]);

        $machineTrackReport = new IntegrationReportsTypes();
        $machineTrackReport->name = IntegrationReportsTypes::MACHINE_TRACK;
        $machineTrackReport->execution = IntegrationReportsTypes::ON_REQUEST;
        $machineTrackReport->period = null;
        $machineTrackReport->url = $integrationAddressBaseUrl;
        $reportTemplate = $reportTypeTemplateMap[IntegrationReportsTypes::MACHINE_TRACK];
        $machineTrackReport->params = json_encode([
            'exec_params' => [
                'reportResourceId' => $wialonResourceId,
                'reportTemplateId' => $wialonReportTemplatesId[$reportTemplate] ?? null,
                'reportTemplate' => null,
                'reportObjectId' => $wialonUnitGroupId,
                'reportObjectSecId' => 0,
                'interval' => [
                    'flags' => 0,
                    'from' => 0,
                    'to' => 0,
                ],
                'reportObjectIdList' => [],
            ],
            'exec_result_params' => [
                'tableIndex' => 0,
                'config' => [
                    'type' => 'range',
                    'data' => [
                        'from' => 0,
                        'to' => 0,
                        'level' => 2,
                        'flat' => 1,
                    ],
                ],
            ],
        ]);
        $machineTrackReport->save();
        $integration->reports()->create([
            'integration_reports_types_id' => $machineTrackReport->id,
        ]);

        $implementsSyncReport = new IntegrationReportsTypes();
        $implementsSyncReport->name = IntegrationReportsTypes::IMPLEMENTS_SYNC;
        $implementsSyncReport->execution = IntegrationReportsTypes::ON_REQUEST;
        $implementsSyncReport->period = null;
        $implementsSyncReport->url = $integrationAddressBaseUrl;
        $implementsSyncReport->params = json_encode([
            'spec' => [
                'itemsType' => 'avl_resource',
                'propName' => 'sys_id ',
                'propValueMask' => '',
                'sortType' => 'sys_id ',
                'propType' => 'list ',
            ],
            'force' => 1,
            'flags' => 65536,
            'from' => 0,
            'to' => 1,
        ]);
        $implementsSyncReport->save();
        $integration->reports()->create([
            'integration_reports_types_id' => $implementsSyncReport->id,
        ]);
    }
}
